<?php
/**
 * Checkout Form
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/form-checkout.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.4.0
 */

if (!defined('ABSPATH')) {
	exit;
}

do_action('woocommerce_before_checkout_form', $checkout);

// If checkout registration is disabled and not logged in, the user cannot checkout.
if (!$checkout->is_registration_enabled() && $checkout->is_registration_required() && !is_user_logged_in()) {
	echo esc_html(apply_filters('woocommerce_checkout_must_be_logged_in_message', __('You must be logged in to checkout.', 'woocommerce')));
	return;
}

?>

<form name="checkout" method="post" class="checkout woocommerce-checkout"
	action="<?php echo esc_url(wc_get_checkout_url()); ?>" enctype="multipart/form-data"
	aria-label="<?php echo esc_attr__('Checkout', 'woocommerce'); ?>">

	<?php if ($checkout->get_checkout_fields()): ?>

		<?php do_action('woocommerce_checkout_before_customer_details'); ?>

		<div class="col2-set" id="customer_details">
			<div class="col-1">
				<?php do_action('woocommerce_checkout_billing'); ?>
				<?php do_action('woocommerce_checkout_shipping'); ?>
			</div>

			<div class="col-2">
				<?php do_action('woocommerce_checkout_before_order_review_heading'); ?>

				<h2 id="order_review_heading" class="h5 font-druk"><?php esc_html_e('Your order', 'woocommerce'); ?></h2>

				<?php do_action('woocommerce_checkout_before_order_review'); ?>

				<div id="order_review" class="woocommerce-checkout-review-order">
					<?php do_action('woocommerce_checkout_order_review'); ?>
				</div>

				<?php do_action('woocommerce_checkout_after_order_review'); ?>
			</div>
		</div>

		<?php do_action('woocommerce_checkout_after_customer_details'); ?>

	<?php endif; ?>



</form>

<?php do_action('woocommerce_after_checkout_form', $checkout); ?>

<!-- Privacy Policy Modal -->
<div id="privacy-policy-modal" class="privacy-modal" style="display: none;">
	<div class="privacy-modal-content">
		<div class="privacy-modal-header">
			<h3>Privacy Policy Agreement</h3>
		</div>
		<div class="privacy-modal-body">
			<p>Sebelum melanjutkan checkout, mohon baca dan setujui kebijakan privasi kami.</p>
			<div class="privacy-policy-text">
				<p>Kami menghargai privasi Anda dan berkomitmen untuk melindungi data pribadi Anda. Dengan melanjutkan
					checkout, Anda menyetujui:</p>
				<ul>
					<li>Pengumpulan dan penggunaan data pribadi untuk memproses pesanan Anda</li>
					<li>Penyimpanan informasi kontak untuk keperluan pengiriman dan komunikasi</li>
					<li>Penggunaan data untuk meningkatkan layanan kami</li>
				</ul>
				<p>Data Anda akan dijaga kerahasiaannya dan tidak akan dibagikan kepada pihak ketiga tanpa persetujuan
					Anda.</p>
			</div>
		</div>
		<div class="privacy-modal-footer">
			<button type="button" id="privacy-agree-btn" class="btn btn-primary">Saya Setuju</button>
			<button type="button" id="privacy-decline-btn" class="btn btn-secondary">Tidak Setuju</button>
		</div>
	</div>
</div>

<style>
	.privacy-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 9999;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.privacy-modal-content {
		background: white;
		border-radius: 8px;
		max-width: 500px;
		width: 90%;
		max-height: 80vh;
		overflow-y: auto;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
	}

	.privacy-modal-header {
		padding: 20px 20px 10px;
		border-bottom: 1px solid #eee;
	}

	.privacy-modal-header h3 {
		margin: 0;
		color: #333;
		font-size: 1.2em;
	}

	.privacy-modal-body {
		padding: 20px;
	}

	.privacy-policy-text {
		background: #f9f9f9;
		padding: 15px;
		border-radius: 5px;
		margin: 15px 0;
		max-height: 200px;
		overflow-y: auto;
		border: 1px solid #ddd;
	}

	.privacy-policy-text ul {
		margin: 10px 0;
		padding-left: 20px;
	}

	.privacy-policy-text li {
		margin: 5px 0;
	}

	.privacy-modal-footer {
		padding: 15px 20px 20px;
		text-align: right;
		border-top: 1px solid #eee;
	}

	.privacy-modal-footer .btn {
		margin-left: 10px;
		padding: 10px 20px;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 14px;
	}

	.btn-primary {
		background-color: #007cba;
		color: white;
	}

	.btn-primary:hover {
		background-color: #005a87;
	}

	.btn-secondary {
		background-color: #6c757d;
		color: white;
	}

	.btn-secondary:hover {
		background-color: #545b62;
	}

	/* Disable checkout form when modal is open */
	.checkout-disabled {
		pointer-events: none;
		opacity: 0.5;
	}
</style>

<script>
	document.addEventListener('DOMContentLoaded', function () {
		// Check if privacy policy has been agreed to
		const privacyAgreed = localStorage.getItem('privacy_policy_agreed');

		if (!privacyAgreed) {
			showPrivacyModal();
		}

		function showPrivacyModal() {
			const modal = document.getElementById('privacy-policy-modal');
			const checkoutForm = document.querySelector('.checkout');

			modal.style.display = 'flex';
			if (checkoutForm) {
				checkoutForm.classList.add('checkout-disabled');
			}
		}

		function hidePrivacyModal() {
			const modal = document.getElementById('privacy-policy-modal');
			const checkoutForm = document.querySelector('.checkout');

			modal.style.display = 'none';
			if (checkoutForm) {
				checkoutForm.classList.remove('checkout-disabled');
			}
		}

		// Handle agree button
		document.getElementById('privacy-agree-btn').addEventListener('click', function () {
			localStorage.setItem('privacy_policy_agreed', 'true');
			hidePrivacyModal();
		});

		// Handle decline button
		document.getElementById('privacy-decline-btn').addEventListener('click', function () {
			alert('Anda harus menyetujui kebijakan privasi untuk melanjutkan checkout.');
			// Optionally redirect to another page
			// window.location.href = '/';
		});

		// Prevent closing modal by clicking outside
		document.getElementById('privacy-policy-modal').addEventListener('click', function (e) {
			if (e.target === this) {
				alert('Mohon baca dan setujui kebijakan privasi untuk melanjutkan.');
			}
		});
	});
</script>